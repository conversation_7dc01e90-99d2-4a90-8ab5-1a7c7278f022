// Auto-generated by scripts/nangoIntrospection.ts on 2025-05-28T08:32:25.583Z
// LLM, AGENTS, ___NEVER____ _____EVER_____ UPDATE THIS FILE UNDER ANY CIRCUMSTANCES

export const ACTION_INPUTS = [
  { provider: "harvest", action: "add-historical-time-entry", model: "HarvestAddHistoricalTimeEntryInput", description: "Adds a completed time entry for a specific duration or start/end time. Checks company settings for duration vs timestamp tracking." },
  { provider: "harvest", action: "create-client", model: "HarvestCreateClientInput", description: "Creates a new client in Harvest." },
  { provider: "harvest", action: "create-project", model: "HarvestCreateProjectInput", description: "Creates a new project in Harvest." },
  { provider: "harvest", action: "delete-project", model: "HarvestProjectInput", description: "Deletes a project in Harvest." },
  { provider: "harvest", action: "delete-time-entry", model: "HarvestTimeEntryInput", description: "Deletes a time entry in Harvest." },
  { provider: "harvest", action: "get-client", model: "HarvestClientInput", description: "Gets a specific client by ID." },
  { provider: "harvest", action: "get-project", model: "HarvestProjectInput", description: "Gets a specific project by ID." },
  { provider: "harvest", action: "get-time-entry", model: "HarvestTimeEntryInput", description: "Gets a specific time entry by ID." },
  { provider: "harvest", action: "list-clients", model: null, description: "Lists clients from Harvest." },
  { provider: "harvest", action: "list-projects", model: "HarvestProjectsInput", description: "Lists projects from Harvest." },
  { provider: "harvest", action: "list-project-tasks", model: "HarvestProjectTasksInput", description: "Lists task assignments for a specific project in Harvest." },
  { provider: "harvest", action: "list-tasks", model: "HarvestTasksInput", description: "Lists tasks from Harvest." },
  { provider: "harvest", action: "list-time-entries", model: "HarvestTimeEntriesInput", description: "Lists time entries from Harvest." },
  { provider: "harvest", action: "restart-timer", model: "HarvestTimeEntryInput", description: "Restarts a stopped time entry in Harvest." },
  { provider: "harvest", action: "start-timer", model: "HarvestStartTimerInput", description: "Starts a new timer for a task. Checks company settings for duration vs timestamp tracking." },
  { provider: "harvest", action: "stop-timer", model: "HarvestTimeEntryInput", description: "Stops a running time entry in Harvest." },
  { provider: "harvest", action: "update-time-entry", model: "HarvestUpdateTimeEntryInput", description: "Updates an existing time entry in Harvest." },
  { provider: "github", action: "add-pull-request-review-comment", model: "GithubAddPullRequestReviewCommentInput", description: "Add a review comment to a pull request." },
  { provider: "github", action: "create-issue", model: "GithubCreateIssueInput", description: "Creates a new issue in a repository." },
  { provider: "github", action: "create-organization-repository", model: "GithubCreateOrganizationRepositoryInput", description: "Creates a new repository within a specified organization." },
  { provider: "github", action: "create-pull-request", model: "GithubCreatePullRequestInput", description: "Create a new pull request in a GitHub repository." },
  { provider: "github", action: "create-pull-request-review", model: "GithubCreatePullRequestReviewInput", description: "Submit a review on a pull request." },
  { provider: "github", action: "create-repository", model: "GithubCreateRepositoryInput", description: "Creates a new repository for the authenticated user. After successful creation, describe to the user how they can push to it inluding ssh url." },
  { provider: "github", action: "delete-repository", model: "GithubRepositoryInput", description: "Deletes a repository." },
  { provider: "github", action: "get-issue", model: "GithubIssueInput", description: "Gets a specific issue by number." },
  { provider: "github", action: "get-pull-request", model: "GithubPullRequestInput", description: "Get details of a specific pull request in a GitHub repository." },
  { provider: "github", action: "get-pull-request-comments", model: "GithubPullRequestInput", description: "Get the review comments on a pull request." },
  { provider: "github", action: "get-pull-request-files", model: "GithubPullRequestInput", description: "Get the files changed in a specific pull request." },
  { provider: "github", action: "get-pull-request-status", model: "GithubPullRequestInput", description: "Get the combined status of all status checks for a pull request." },
  { provider: "github", action: "get-repository", model: "GithubRepositoryInput", description: "Gets a specific repository by owner and name." },
  { provider: "github", action: "list-branches", model: "GithubListBranchesInput", description: "List branches in a repository." },
  { provider: "github", action: "list-issues", model: "GithubIssuesInput", description: "Lists issues for a repository." },
  { provider: "github", action: "list-pull-requests", model: "GithubListPullRequestsInput", description: "List pull requests in a GitHub repository." },
  { provider: "github", action: "list-repos", model: null, description: "List github repos from an organization." },
  { provider: "github", action: "list-repositories", model: null, description: "Lists repositories for the authenticated user." },
  { provider: "github", action: "merge-pull-request", model: "GithubMergePullRequestInput", description: "Merge a pull request in a GitHub repository." },
  { provider: "github", action: "update-issue", model: "GithubUpdateIssueInput", description: "Updates an existing issue." },
  { provider: "github", action: "update-pull-request", model: "GithubUpdatePullRequestInput", description: "Update an existing pull request in a GitHub repository." },
  { provider: "github", action: "update-pull-request-branch", model: "GithubUpdatePullRequestBranchInput", description: "Update the branch of a pull request with the latest changes from the base branch." },
  { provider: "github", action: "update-repository", model: "GithubUpdateRepositoryInput", description: "Updates an existing repository." },
  { provider: "github", action: "write-file", model: "GithubWriteFileInput", description: "Write content to a particular github file within a repo. If" },
  { provider: "slack", action: "add-reaction-as-user", model: "SlackAddReactionInput", description: "Adds an emoji reaction to a message as the authenticated user." },
  { provider: "slack", action: "get-channel-history", model: "SlackGetChannelHistoryInput", description: "Retrieves message history for a specific channel." },
  { provider: "slack", action: "get-message-permalink", model: "SlackGetPermalinkInput", description: "Retrieves a permalink for a specific message." },
  { provider: "slack", action: "get-user-info", model: "SlackGetUserInfoInput", description: "Retrieves information about a specific user." },
  { provider: "slack", action: "list-channels", model: "SlackListChannelsInput", description: "Lists channels in Slack." },
  { provider: "slack", action: "search-messages", model: "SlackSearchMessagesInput", description: "Searches for messages matching a query." },
  { provider: "slack", action: "send-message-as-user", model: "SlackSendMessageInput", description: "Sends a message to a Slack channel as the authenticated user." },
  { provider: "slack", action: "update-message-as-user", model: "SlackUpdateMessageInput", description: "Updates an existing message in a channel as the authenticated user." },
  { provider: "google-calendar", action: "create-event", model: "GoogleCalendarEventInput", description: "Creates a new event in Google Calendar. Can either be full-day or time-based." },
  { provider: "google-calendar", action: "delete-event", model: "GoogleCalendarEventDeleteInput", description: "Deletes an event from Google Calendar." },
  { provider: "google-calendar", action: "list-calendars", model: null, description: "Lists all calendars available to the authenticated user." },
  { provider: "google-calendar", action: "list-events", model: "GoogleCalendarEventsInput", description: "Lists events from a specified calendar. By default will only include future events. To include past events, set the timeMin to some time in the past." },
  { provider: "google-calendar", action: "update-event", model: "GoogleCalendarEventUpdateInput", description: "Updates an event in Google Calendar." },
  { provider: "google-mail", action: "compose-draft", model: "GmailDraftInput", description: "Creates a new draft email in Gmail." },
  { provider: "google-mail", action: "compose-draft-reply", model: "GmailReplyDraftInput", description: "Creates a new draft email that is a reply to an existing email." },
  { provider: "google-mail", action: "delete-message", model: "GmailMessageIdInput", description: "Permanently deletes the specified message. Bypasses Trash." },
  { provider: "google-mail", action: "get-message", model: "GmailGetMessageInput", description: "Retrieves a specific email by ID." },
  { provider: "google-mail", action: "list-messages", model: "GmailListMessagesInput", description: "Lists emails from Gmail inbox with optional filtering." },
  { provider: "google-mail", action: "modify-message-labels", model: "GmailModifyMessageLabelsInput", description: "Modifies the labels applied to a specific message." },
  { provider: "google-mail", action: "send-email", model: "GmailSendEmailInput", description: "Sends an email via Gmail." },
  { provider: "google-mail", action: "trash-message", model: "GmailMessageIdInput", description: "Moves the specified message to the trash." },
  { provider: "google-mail", action: "untrash-message", model: "GmailMessageIdInput", description: "Removes the specified message from the trash." },
  { provider: "dropbox", action: "copy-file", model: "DropboxCopyInput", description: "Copy a file or folder to a different location in Dropbox and return metadata of the copied item" },
  { provider: "dropbox", action: "create-folder", model: "DropboxCreateFolderInput", description: "Create a new folder in Dropbox and return folder metadata" },
  { provider: "dropbox", action: "delete-file", model: "DropboxDeleteInput", description: "Delete a file or folder in Dropbox and return metadata of the deleted item" },
  { provider: "dropbox", action: "fetch-file", model: "Anonymous_dropbox_action_fetchfile_input", description: "Fetches the content of a file given its ID, processes the data using a response stream, and encodes it into a base64 string. This base64-encoded string can be used to recreate the file in its original format using an external tool." },
  { provider: "dropbox", action: "get-file", model: "DropboxGetFileInput", description: "Get file metadata and a download URL (not the actual file content)" },
  { provider: "dropbox", action: "list-files", model: "DropboxListFilesInput", description: "List files and folders in a Dropbox directory" },
  { provider: "dropbox", action: "move-file", model: "DropboxMoveInput", description: "Move a file or folder to a different location in Dropbox and return metadata of the moved item" },
  { provider: "dropbox", action: "search-files", model: "DropboxSearchInput", description: "Search for files and folders in Dropbox by filename or content" },
  { provider: "dropbox", action: "upload-file", model: "DropboxUploadFileInput", description: "Upload string content as a file to Dropbox" },
  { provider: "notion", action: "create-database", model: "NotionCreateDatabaseInput", description: "Creates a new Notion database as a subpage of a specified page." },
  { provider: "notion", action: "create-page", model: "NotionCreatePageInput", description: "Creates a new Notion page." },
  { provider: "notion", action: "get-database", model: "NotionGetDatabaseInput", description: "Retrieves a specific Notion Database object by its ID." },
  { provider: "notion", action: "get-page", model: "NotionGetPageInput", description: "Retrieves a specific Notion Page object by its ID." },
  { provider: "notion", action: "query-database", model: "NotionQueryDatabaseInput", description: "Queries a Notion database for pages, with optional filters and sorts." },
  { provider: "notion", action: "search", model: "NotionSearchInput", description: "Searches pages and databases in Notion. IMPORTANT - Use \"\" to search for everything." },
  { provider: "notion", action: "update-database", model: "NotionUpdateDatabaseInput", description: "Updates properties of an existing Notion database. ALSO USED TO \"delete\" a database, set archive to true." },
  { provider: "notion", action: "update-page", model: "NotionUpdatePageInput", description: "Updates properties of an existing Notion page. ALSO USED TO \"delete\" a page, set archive to true." },
  { provider: "google-docs", action: "create-document", model: "GoogleDocsCreateDocumentInput", description: "Creates a blank Google Document." },
  { provider: "google-docs", action: "fetch-document", model: "DocumentId", description: "Fetches the content of a document given its ID." },
  { provider: "google-docs", action: "get-document", model: "GoogleDocsGetDocumentInput", description: "Retrieves a specific Google Document." },
  { provider: "google-docs", action: "update-document", model: "GoogleDocsUpdateDocumentInput", description: "Applies batch updates to a Google Document." },
  { provider: "linear", action: "create-issue", model: "LinearCreateIssueInput", description: "Creates a new issue in Linear." },
  { provider: "linear", action: "create-project", model: "LinearCreateProjectInput", description: "Creates a new project in Linear." },
  { provider: "linear", action: "delete-issue", model: "LinearIssueInput", description: "Deletes an issue in Linear." },
  { provider: "linear", action: "fetch-models", model: null, description: "Introspection endpoint to fetch the models available" },
  { provider: "linear", action: "get-issue", model: "LinearIssueInput", description: "Gets a specific issue by ID." },
  { provider: "linear", action: "get-project", model: "LinearProjectInput", description: "Gets a specific project by ID." },
  { provider: "linear", action: "get-team", model: "LinearTeamInput", description: "Gets a specific team by ID." },
  { provider: "linear", action: "list-issues", model: "LinearIssuesInput", description: "Lists issues from Linear." },
  { provider: "linear", action: "list-projects", model: "LinearProjectsInput", description: "List all projects from Linear" },
  { provider: "linear", action: "list-teams", model: "LinearTeamsInput", description: "Lists teams from Linear." },
  { provider: "linear", action: "update-issue", model: "LinearUpdateIssueInput", description: "Updates an existing issue in Linear." },
  { provider: "linear", action: "update-project", model: "LinearUpdateProjectInput", description: "Updates an existing project in Linear." },
  { provider: "google-sheet", action: "create-sheet", model: "GoogleSheetCreateInput", description: "Creates a new Google Sheet with optional initial data." },
  { provider: "google-sheet", action: "fetch-spreadsheet", model: "SpreadsheetId", description: "Fetches the content of a spreadsheet given its ID." },
  { provider: "google-sheet", action: "update-sheet", model: "GoogleSheetUpdateInput", description: "Updates an existing Google Sheet with new data." },
  { provider: "google-drive", action: "fetch-document", model: "IdEntity", description: "Fetches the content of a file given its ID, processes the data using" },
  { provider: "google-drive", action: "fetch-google-doc", model: "IdEntity", description: "Fetches the content of a native google document given its ID. Outputs" },
  { provider: "google-drive", action: "fetch-google-sheet", model: "IdEntity", description: "Fetches the content of a native google spreadsheet given its ID. Outputs" },
  { provider: "google-drive", action: "folder-content", model: "FolderContentInput", description: "Fetches the top-level content (files and folders) of a folder given its ID." },
  { provider: "google-drive", action: "list-documents", model: "ListDocumentsInput", description: "Lists documents in Google Drive with optional filtering by folder ID and document type." },
  { provider: "google-drive", action: "list-root-folders", model: null, description: "Lists folders at the root level of Google Drive." },
  { provider: "google-drive", action: "upload-document", model: "UploadFileInput", description: "Uploads a file to Google Drive. The file is uploaded to the root directory" },
  { provider: "twitter-v2", action: "get-user-profile", model: null, description: "Gets the authenticated user's profile information from X." },
  { provider: "twitter-v2", action: "send-post", model: "XSocialPostInput", description: "Sends a new post to X." },
  { provider: "linkedin", action: "get-user-profile", model: null, description: "Gets the authenticated user's profile information from LinkedIn." },
  { provider: "linkedin", action: "post", model: "LinkedinVideoPost", description: "Create a linkedin post with an optional video" },
  { provider: "linkedin", action: "send-post", model: "LinkedInPostInput", description: "Creates a new post on LinkedIn." },
] as const;

export const ACTION_OUTPUTS = [
  { provider: "harvest", action: "add-historical-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "create-client", model: "HarvestClient", description: "" },
  { provider: "harvest", action: "create-project", model: "HarvestProject", description: "" },
  { provider: "harvest", action: "delete-project", model: "HarvestDeleteProjectOutput", description: "" },
  { provider: "harvest", action: "delete-time-entry", model: "HarvestDeleteTimeEntryOutput", description: "" },
  { provider: "harvest", action: "get-client", model: "HarvestClient", description: "" },
  { provider: "harvest", action: "get-project", model: "HarvestProject", description: "" },
  { provider: "harvest", action: "get-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "list-clients", model: "HarvestClientList", description: "" },
  { provider: "harvest", action: "list-projects", model: "HarvestProjectList", description: "" },
  { provider: "harvest", action: "list-project-tasks", model: "HarvestProjectTaskList", description: "" },
  { provider: "harvest", action: "list-tasks", model: "HarvestTaskList", description: "" },
  { provider: "harvest", action: "list-time-entries", model: "HarvestTimeEntryList", description: "" },
  { provider: "harvest", action: "restart-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "start-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "stop-timer", model: "HarvestTimeEntry", description: "" },
  { provider: "harvest", action: "update-time-entry", model: "HarvestTimeEntry", description: "" },
  { provider: "github", action: "add-pull-request-review-comment", model: "GithubPullRequestComment", description: "" },
  { provider: "github", action: "create-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "create-organization-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "create-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "create-pull-request-review", model: "GithubPullRequestReview", description: "" },
  { provider: "github", action: "create-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "delete-repository", model: "GithubDeleteRepositoryOutput", description: "" },
  { provider: "github", action: "get-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "get-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "get-pull-request-comments", model: "GithubPullRequestCommentList", description: "" },
  { provider: "github", action: "get-pull-request-files", model: "GithubPullRequestFileList", description: "" },
  { provider: "github", action: "get-pull-request-status", model: "GithubCombinedStatus", description: "" },
  { provider: "github", action: "get-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "list-branches", model: "GithubBranchList", description: "" },
  { provider: "github", action: "list-issues", model: "GithubIssueList", description: "" },
  { provider: "github", action: "list-pull-requests", model: "GithubPullRequestList", description: "" },
  { provider: "github", action: "list-repos", model: "GithubRepo", description: "" },
  { provider: "github", action: "list-repositories", model: "GithubRepositoryList", description: "" },
  { provider: "github", action: "merge-pull-request", model: "GithubMergeResult", description: "" },
  { provider: "github", action: "update-issue", model: "GithubIssue", description: "" },
  { provider: "github", action: "update-pull-request", model: "GithubPullRequest", description: "" },
  { provider: "github", action: "update-pull-request-branch", model: "GithubBranchUpdateResult", description: "" },
  { provider: "github", action: "update-repository", model: "GithubRepository", description: "" },
  { provider: "github", action: "write-file", model: "GithubWriteFileActionResult", description: "" },
  { provider: "slack", action: "add-reaction-as-user", model: "SlackReactionOutput", description: "" },
  { provider: "slack", action: "get-channel-history", model: "SlackMessageList", description: "" },
  { provider: "slack", action: "get-message-permalink", model: "SlackPermalinkOutput", description: "" },
  { provider: "slack", action: "get-user-info", model: "SlackUserInfo", description: "" },
  { provider: "slack", action: "list-channels", model: "SlackConversationsList", description: "" },
  { provider: "slack", action: "search-messages", model: "SlackSearchResultList", description: "" },
  { provider: "slack", action: "send-message-as-user", model: "SlackSendMessageOutput", description: "" },
  { provider: "slack", action: "update-message-as-user", model: "SlackUpdateMessageOutput", description: "" },
  { provider: "google-calendar", action: "create-event", model: "GoogleCalendarEventOutput", description: "" },
  { provider: "google-calendar", action: "delete-event", model: null, description: "Deletes an event from Google Calendar." },
  { provider: "google-calendar", action: "list-calendars", model: "GoogleCalendarList", description: "" },
  { provider: "google-calendar", action: "list-events", model: "GoogleCalendarEventList", description: "" },
  { provider: "google-calendar", action: "update-event", model: "GoogleCalendarEventOutput", description: "" },
  { provider: "google-mail", action: "compose-draft", model: "GmailDraftOutput", description: "" },
  { provider: "google-mail", action: "compose-draft-reply", model: "GmailReplyDraftOutput", description: "" },
  { provider: "google-mail", action: "delete-message", model: "GmailDeleteMessageOutput", description: "" },
  { provider: "google-mail", action: "get-message", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "list-messages", model: "GmailMessageList", description: "" },
  { provider: "google-mail", action: "modify-message-labels", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "send-email", model: "GmailSendEmailOutput", description: "" },
  { provider: "google-mail", action: "trash-message", model: "GmailMessage", description: "" },
  { provider: "google-mail", action: "untrash-message", model: "GmailMessage", description: "" },
  { provider: "dropbox", action: "copy-file", model: "DropboxEntry", description: "" },
  { provider: "dropbox", action: "create-folder", model: "DropboxFolder", description: "" },
  { provider: "dropbox", action: "delete-file", model: "DropboxDeleteResult", description: "" },
  { provider: "dropbox", action: "fetch-file", model: "Anonymous_dropbox_action_fetchfile_output", description: "" },
  { provider: "dropbox", action: "get-file", model: "DropboxFile", description: "" },
  { provider: "dropbox", action: "list-files", model: "DropboxFileList", description: "" },
  { provider: "dropbox", action: "move-file", model: "DropboxEntry", description: "" },
  { provider: "dropbox", action: "search-files", model: "DropboxSearchResult", description: "" },
  { provider: "dropbox", action: "upload-file", model: "DropboxFile", description: "" },
  { provider: "notion", action: "create-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "create-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "notion", action: "get-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "get-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "notion", action: "query-database", model: "NotionQueryDatabaseOutput", description: "" },
  { provider: "notion", action: "search", model: "NotionSearchOutput", description: "" },
  { provider: "notion", action: "update-database", model: "NotionDatabase", description: "" },
  { provider: "notion", action: "update-page", model: "NotionPageOrDatabase", description: "" },
  { provider: "google-docs", action: "create-document", model: "GoogleDocsDocument", description: "" },
  { provider: "google-docs", action: "fetch-document", model: "Document", description: "" },
  { provider: "google-docs", action: "get-document", model: "GoogleDocsDocument", description: "" },
  { provider: "google-docs", action: "update-document", model: "GoogleDocsUpdateDocumentOutput", description: "" },
  { provider: "linear", action: "create-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "create-project", model: "LinearProject", description: "" },
  { provider: "linear", action: "delete-issue", model: "LinearDeleteIssueOutput", description: "" },
  { provider: "linear", action: "fetch-models", model: "ModelResponse", description: "" },
  { provider: "linear", action: "get-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "get-project", model: "LinearProject", description: "" },
  { provider: "linear", action: "get-team", model: "LinearTeam", description: "" },
  { provider: "linear", action: "list-issues", model: "LinearIssueList", description: "" },
  { provider: "linear", action: "list-projects", model: "LinearProjectList", description: "" },
  { provider: "linear", action: "list-teams", model: "LinearTeamList", description: "" },
  { provider: "linear", action: "update-issue", model: "LinearIssue", description: "" },
  { provider: "linear", action: "update-project", model: "LinearProject", description: "" },
  { provider: "google-sheet", action: "create-sheet", model: "GoogleSheetCreateOutput", description: "" },
  { provider: "google-sheet", action: "fetch-spreadsheet", model: "Spreadsheet", description: "" },
  { provider: "google-sheet", action: "update-sheet", model: "GoogleSheetUpdateOutput", description: "" },
  { provider: "google-drive", action: "fetch-document", model: "Anonymous_googledrive_action_fetchdocument_output", description: "" },
  { provider: "google-drive", action: "fetch-google-doc", model: "JSONDocument", description: "" },
  { provider: "google-drive", action: "fetch-google-sheet", model: "JSONSpreadsheet", description: "" },
  { provider: "google-drive", action: "folder-content", model: "FolderContent", description: "" },
  { provider: "google-drive", action: "list-documents", model: "GoogleDriveDocumentList", description: "" },
  { provider: "google-drive", action: "list-root-folders", model: "GoogleDriveFolderList", description: "" },
  { provider: "google-drive", action: "upload-document", model: "GoogleDocument", description: "" },
  { provider: "twitter-v2", action: "get-user-profile", model: "XSocialUserProfile", description: "" },
  { provider: "twitter-v2", action: "send-post", model: "XSocialPostOutput", description: "" },
  { provider: "linkedin", action: "get-user-profile", model: "LinkedInUserProfile", description: "" },
  { provider: "linkedin", action: "post", model: "CreateLinkedInPostWithVideoResponse", description: "" },
  { provider: "linkedin", action: "send-post", model: "LinkedInPostOutput", description: "" },
] as const;
