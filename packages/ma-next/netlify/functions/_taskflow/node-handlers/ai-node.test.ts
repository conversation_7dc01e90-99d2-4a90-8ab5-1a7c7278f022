import { strictEqual, deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { executeAiNode, AiNodeDependencies } from './ai-node';
import { NodeExecutionSubtypeParams } from '../types';

// Mocks
let generateTextCalls: any[] = [];
const mockGenerateText = async (opts: any) => {
  generateTextCalls.push(opts);
  return { text: 'This is a generated text response.' };
};

let generateObjectCalls: any[] = [];
const mockGenerateObject = async (opts: any) => {
  generateObjectCalls.push(opts);
  return { object: { title: 'Generated Title', content: 'Generated content' } };
};

let jsonSchemaCalls: any[] = [];
const mockJsonSchema = (schema: any) => {
  jsonSchemaCalls.push(schema);
  return schema;
};

const mockCreateOpenAI = () => ({
  responses: (model: string) => ({ model }) as any,
});

const deps: AiNodeDependencies = {
  createOpenAI: mockCreateOpenAI,
  generateText: mockGenerateText,
  generateObject: mockGenerateObject,
  jsonSchema: mockJsonSchema,
};

process.env.OPENAI_API_KEY = 'test-key';

test('AI Node - generates text when no outputSchema is provided', async () => {
  generateTextCalls = [];
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'ai.simple',
      subtype: 'simple',
      parameters: {
        system: 'You are a helpful assistant.',
        prompt: 'Tell me about AI.',
        model: 'gpt-4o',
      },
    },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
  };

  const result = await executeAiNode(params, deps);
  strictEqual(result.status, 'SUCCESS');
  strictEqual(generateTextCalls.length, 1);
  deepStrictEqual(result.result, 'This is a generated text response.');
});

test('AI Node - generates structured output when outputSchema is provided', async () => {
  generateObjectCalls = [];
  jsonSchemaCalls = [];
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'ai.simple',
      subtype: 'simple',
      parameters: {
        system: 'You are a helpful assistant.',
        prompt: 'Generate a blog post.',
        model: 'gpt-4o',
        outputSchema: {
          type: 'object',
          properties: { title: { type: 'string' }, content: { type: 'string' } },
          required: ['title', 'content'],
        },
      },
    },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
    userProfile: {},
  };

  const result = await executeAiNode(params, deps);
  strictEqual(result.status, 'SUCCESS');
  strictEqual(generateObjectCalls.length, 1);
  strictEqual(jsonSchemaCalls.length, 1);
  deepStrictEqual(result.result, { title: 'Generated Title', content: 'Generated content' });
});

test('AI Node - throws error for unsupported subtype', async () => {
  const params: NodeExecutionSubtypeParams = {
    node: { id: 'test-node', type: 'ai.unsupported', subtype: 'unsupported', parameters: {} },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
  };

  try {
    await executeAiNode(params, deps);
  } catch (err: any) {
    strictEqual(err.message, 'Unsupported AI node subtype: unsupported');
    return;
  }
  throw new Error('Expected error was not thrown');
});
