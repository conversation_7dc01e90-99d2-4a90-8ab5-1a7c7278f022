import { strictEqual, deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { executeHitlFormNode } from './hitl-form-node';
import { NodeExecutionSubtypeParams } from '../types';

test('HITL Form Node - pauses execution when no resumePayload is provided', async () => {
  const formParameters = {
    prompt: 'Please select a project',
    fields: [
      {
        type: 'select',
        name: 'project_id',
        options: [
          { value: '1', label: 'Project 1' },
          { value: '2', label: 'Project 2' },
        ],
      },
    ],
  };
  const params: NodeExecutionSubtypeParams = {
    node: { id: 'test-node', type: 'hitl.form', subtype: 'form', parameters: formParameters },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
  };
  const result = await executeHitlFormNode(params);
  strictEqual(result.status, 'PAUSED');
  strictEqual(result.hitlParams?.type, 'form');
  deepStrictEqual(result.hitlParams?.parameters, formParameters);
});

test('HITL Form Node - completes with resumePayload when provided', async () => {
  const formData = { project_id: '2' };
  const params: NodeExecutionSubtypeParams = {
    node: {
      id: 'test-node',
      type: 'hitl.form',
      subtype: 'form',
      parameters: { prompt: 'Prompt', fields: [] },
    },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
    resumePayload: formData,
  };
  const result = await executeHitlFormNode(params);
  strictEqual(result.status, 'SUCCESS');
  deepStrictEqual(result.result, formData);
});

test('HITL Form Node - throws error for unsupported subtype', async () => {
  const params: NodeExecutionSubtypeParams = {
    node: { id: 'test-node', type: 'hitl.unsupported', subtype: 'unsupported', parameters: {} },
    supabase: {} as any,
    executionId: 'exec-123',
    userId: 'user-123',
  };
  try {
    await executeHitlFormNode(params);
  } catch (err: any) {
    strictEqual(err.message, 'Unsupported hitlForm node subtype: unsupported');
    return;
  }
  throw new Error('Expected error was not thrown');
});
