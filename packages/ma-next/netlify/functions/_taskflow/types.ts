import { executeActionNode } from './node-handlers/action-node';
import { executeHitlFormNode } from './node-handlers/hitl-form-node';
import { executeAiNode, AiNodeDependencies } from './node-handlers/ai-node';
import { AgentNodeDependencies, executeAgentNode } from './node-handlers/agent-node';

/**
 * Node type definitions
 */
export type FirstDotNodeType = 'provider' | 'hitl' | 'ai' | 'agent';
export type NodeType = `provider.${string}.${string}` | `hitl.form` | `ai.simple` | 'agent.aToB';

/**
 * Workflow schema definition
 */
export interface WorkflowSchema {
  nodes: {
    id: string;
    type: NodeType;
    parameters: Record<string, any>;
  }[];
}

/**
 * Execution context and related types
 */
export type NodeId = string;
export type NodeStatus = 'RUNNING' | 'PAUSED' | 'SUCCESS' | 'ERROR';
export type NodeContext = {
  status: NodeStatus;
  type: string;
  parameters?: any;
  output?: any;
  hitl?: any;
  error?: any;
};

export type ExecutionContext = Record<NodeId, NodeContext>;
export type ResumeData = {
  nodeId: string;
  executionId: string;
  data: Record<string, any>;
  force?: boolean;
};

import { DebugTracer } from './debugTracer';
/**
 * Request types for different operations
 */
import { TaskflowSupabaseFacade } from './taskflowSupabaseFacade';

export type BaseTaskflowRequest = {
  supabaseFacade: TaskflowSupabaseFacade;
  userId: string;
};

export type CreateExecutionRequest = BaseTaskflowRequest & {
  taskflowId: string;
  triggerData?: Record<string, any>;
};

export type TriggerWithExecutionRequest = BaseTaskflowRequest & {
  taskflowExecutionId: string;
  triggerData?: Record<string, any>;
};

export type ResumeExecutionRequest = BaseTaskflowRequest & {
  resumeData: ResumeData;
};

export type ExecTaskflowParams = BaseTaskflowRequest & {
  tracer: DebugTracer;
  taskflowId?: string;
  taskflowExecutionId?: string;
  triggerData?: Record<string, any>;
  resumeData?: ResumeData;
  handlers?: Partial<NodeHandlers>;
};

/**
 * Node execution types
 */
export type PreparedData = {
  taskflow: {
    schema: any;
    nodes: WorkflowSchema['nodes']; // note: not necessarily just schema.nodes
  };
  taskflowExecution: {
    id: string;
    context: ExecutionContext;
    triggerData: Record<string, any> | undefined;
    resumeData?: ExecTaskflowParams['resumeData'];
  };
  userProfile: {
    id: string;
    firstName?: string | null;
    lastName?: string | null;
    preferences?: Record<string, any>;
  };
};

type NodeExecutionParamsBase = {
  supabaseFacade: TaskflowSupabaseFacade;
  executionId: string;
  resumePayload?: Record<string, any>; // the actual data
  userId: string;
  userProfile: PreparedData['userProfile'];
  tracer: DebugTracer;
};

export type NodeExecutionParams = NodeExecutionParamsBase & {
  node: WorkflowSchema['nodes'][number];
};

export type NodeExecutionSubtypeParams = NodeExecutionParamsBase & {
  node: WorkflowSchema['nodes'][number] & { subtype: string };
};

export type NodeOutput = {
  status: NodeStatus;
  hitlParams?: any;
  result?: any;
  error?: any;
};

export type NodeHandlers = {
  executeActionNode?: typeof executeActionNode;
  executeHitlFormNode?: typeof executeHitlFormNode;
  executeAiNode?: typeof executeAiNode;
  executeAgentNode?: typeof executeAgentNode;
} & NodeHandlerDependencies;

export type NodeHandlerDependencies = {
  actionNodeDependencies?: Record<string, any>;
  aiNodeDependencies?: AiNodeDependencies;
  hitlFormNodeDependencies?: Record<string, any>;
  agentNodeDependencies: AgentNodeDependencies;
};

export type { AiNodeDependencies };
