import type { NangoAction, GithubDeleteRepositoryOutput, GithubRepositoryInput } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubRepositoryInput
): Promise<GithubDeleteRepositoryOutput | NangoError> {
  try {
    const { owner, repo } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    await nango.proxy({
      method: 'DELETE',
      endpoint: `/repos/${owner}/${repo}`,
    });

    return {
      success: true,
      message: `Repository ${owner}/${repo} was successfully deleted`,
    };
  } catch (error: any) {
    console.error('Error deleting repository:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while deleting the repository.';
    return { error: { status, message } };
  }
}
