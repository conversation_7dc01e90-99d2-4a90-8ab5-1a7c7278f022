import type { GithubPullRequestCommentList, GithubPullRequestInput, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubPullRequestInput
): Promise<GithubPullRequestCommentList | NangoError> {
  try {
    const { owner, repo, pullNumber } = input;

    const response = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}/pulls/${pullNumber}/comments`,
      params: {
        per_page: 100,
      },
    });

    if (response.status !== 200) {
      console.error('GitHub API Error:', response.status, response.data);
      return {
        error: {
          status: response.status,
          message: `GitHub API Error: Failed to get pull request comments: ${response.status} ${JSON.stringify(response.data)}`,
        },
      };
    }

    return {
      comments: response.data,
    };
  } catch (error: any) {
    console.error('Error getting pull request comments:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while getting the pull request comments.';
    return { error: { status, message } };
  }
}
