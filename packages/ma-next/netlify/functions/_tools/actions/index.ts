// Generated index file for action lookup
import dropboxCopyFile from './dropbox/copy-file';
import dropboxCreateFolder from './dropbox/create-folder';
import dropboxDeleteFile from './dropbox/delete-file';
import dropboxGetFile from './dropbox/get-file';
import dropboxListFiles from './dropbox/list-files';
import dropboxMoveFile from './dropbox/move-file';
import dropboxSearchFiles from './dropbox/search-files';
import dropboxUploadFile from './dropbox/upload-file';
import githubAddPullRequestReviewComment from './github/add-pull-request-review-comment';
import githubCreateIssue from './github/create-issue';
import githubCreateOrganizationRepository from './github/create-organization-repository';
import githubCreatePullRequestReview from './github/create-pull-request-review';
import githubCreatePullRequest from './github/create-pull-request';
import githubCreateRepository from './github/create-repository';
import githubDeleteRepository from './github/delete-repository';
import githubGetIssue from './github/get-issue';
import githubGetPullRequestComments from './github/get-pull-request-comments';
import githubGetPullRequestFiles from './github/get-pull-request-files';
import githubGetPullRequestStatus from './github/get-pull-request-status';
import githubGetPullRequest from './github/get-pull-request';
import githubGetRepository from './github/get-repository';
import githubListBranches from './github/list-branches';
import githubListIssues from './github/list-issues';
import githubListPullRequests from './github/list-pull-requests';
import githubListRepositories from './github/list-repositories';
import githubMergePullRequest from './github/merge-pull-request';
import githubUpdateIssue from './github/update-issue';
import githubUpdatePullRequestBranch from './github/update-pull-request-branch';
import githubUpdatePullRequest from './github/update-pull-request';
import githubUpdateRepository from './github/update-repository';
import googleCalendarCreateEvent from './google-calendar/create-event';
import googleCalendarDeleteEvent from './google-calendar/delete-event';
import googleCalendarListCalendars from './google-calendar/list-calendars';
import googleCalendarListEvents from './google-calendar/list-events';
import googleCalendarUpdateEvent from './google-calendar/update-event';
import googleDocsCreateDocument from './google-docs/create-document';
import googleDocsGetDocument from './google-docs/get-document';
import googleDocsUpdateDocument from './google-docs/update-document';
import googleDriveListDocuments from './google-drive/list-documents';
import googleDriveListRootFolders from './google-drive/list-root-folders';
import googleMailComposeDraftReply from './google-mail/compose-draft-reply';
import googleMailComposeDraft from './google-mail/compose-draft';
import googleMailDeleteMessage from './google-mail/delete-message';
import googleMailGetMessage from './google-mail/get-message';
import googleMailListMessages from './google-mail/list-messages';
import googleMailModifyMessageLabels from './google-mail/modify-message-labels';
import googleMailSearchMessages from './google-mail/search-messages';
import googleMailSendEmail from './google-mail/send-email';
import googleMailTrashMessage from './google-mail/trash-message';
import googleMailUntrashMessage from './google-mail/untrash-message';
import googleSheetCreateSheet from './google-sheet/create-sheet';
import googleSheetUpdateSheet from './google-sheet/update-sheet';
import harvestAddHistoricalTimeEntry from './harvest/add-historical-time-entry';
import harvestCreateClient from './harvest/create-client';
import harvestCreateProject from './harvest/create-project';
import harvestDeleteProject from './harvest/delete-project';
import harvestDeleteTimeEntry from './harvest/delete-time-entry';
import harvestGetClient from './harvest/get-client';
import harvestGetProject from './harvest/get-project';
import harvestGetTimeEntry from './harvest/get-time-entry';
import harvestListClients from './harvest/list-clients';
import harvestListProjectTasks from './harvest/list-project-tasks';
import harvestListProjects from './harvest/list-projects';
import harvestListTasks from './harvest/list-tasks';
import harvestListTimeEntries from './harvest/list-time-entries';
import harvestRestartTimer from './harvest/restart-timer';
import harvestStartTimer from './harvest/start-timer';
import harvestStopTimer from './harvest/stop-timer';
import harvestUpdateTimeEntry from './harvest/update-time-entry';
import linearCreateIssue from './linear/create-issue';
import linearCreateProject from './linear/create-project';
import linearDeleteIssue from './linear/delete-issue';
import linearGetIssue from './linear/get-issue';
import linearGetProject from './linear/get-project';
import linearGetTeam from './linear/get-team';
import linearListIssues from './linear/list-issues';
import linearListProjects from './linear/list-projects';
import linearListTeams from './linear/list-teams';
import linearUpdateIssue from './linear/update-issue';
import linearUpdateProject from './linear/update-project';
import linkedinGetUserProfile from './linkedin/get-user-profile';
import linkedinSendPost from './linkedin/send-post';
import notionCreateDatabase from './notion/create-database';
import notionCreatePage from './notion/create-page';
import notionGetDatabase from './notion/get-database';
import notionGetPage from './notion/get-page';
import notionQueryDatabase from './notion/query-database';
import notionSearch from './notion/search';
import notionUpdateDatabase from './notion/update-database';
import notionUpdatePage from './notion/update-page';
import slackAddReactionAsUser from './slack/add-reaction-as-user';
import slackGetChannelHistory from './slack/get-channel-history';
import slackGetMessagePermalink from './slack/get-message-permalink';
import slackGetUserInfo from './slack/get-user-info';
import slackListChannels from './slack/list-channels';
import slackSearchMessages from './slack/search-messages';
import slackSendMessageAsUser from './slack/send-message-as-user';
import slackUpdateMessageAsUser from './slack/update-message-as-user';
import twitterV2GetUserProfile from './twitter-v2/get-user-profile';
import twitterV2SendPost from './twitter-v2/send-post';
import xSocialGetUserProfile from './x-social/get-user-profile';
import xSocialSendPost from './x-social/send-post';

const actionMap: Record<string, Function> = {
  ['dropbox:copy-file']: dropboxCopyFile,
  ['dropbox:create-folder']: dropboxCreateFolder,
  ['dropbox:delete-file']: dropboxDeleteFile,
  ['dropbox:get-file']: dropboxGetFile,
  ['dropbox:list-files']: dropboxListFiles,
  ['dropbox:move-file']: dropboxMoveFile,
  ['dropbox:search-files']: dropboxSearchFiles,
  ['dropbox:upload-file']: dropboxUploadFile,
  ['github:add-pull-request-review-comment']: githubAddPullRequestReviewComment,
  ['github:create-issue']: githubCreateIssue,
  ['github:create-organization-repository']: githubCreateOrganizationRepository,
  ['github:create-pull-request-review']: githubCreatePullRequestReview,
  ['github:create-pull-request']: githubCreatePullRequest,
  ['github:create-repository']: githubCreateRepository,
  ['github:delete-repository']: githubDeleteRepository,
  ['github:get-issue']: githubGetIssue,
  ['github:get-pull-request-comments']: githubGetPullRequestComments,
  ['github:get-pull-request-files']: githubGetPullRequestFiles,
  ['github:get-pull-request-status']: githubGetPullRequestStatus,
  ['github:get-pull-request']: githubGetPullRequest,
  ['github:get-repository']: githubGetRepository,
  ['github:list-branches']: githubListBranches,
  ['github:list-issues']: githubListIssues,
  ['github:list-pull-requests']: githubListPullRequests,
  ['github:list-repositories']: githubListRepositories,
  ['github:merge-pull-request']: githubMergePullRequest,
  ['github:update-issue']: githubUpdateIssue,
  ['github:update-pull-request-branch']: githubUpdatePullRequestBranch,
  ['github:update-pull-request']: githubUpdatePullRequest,
  ['github:update-repository']: githubUpdateRepository,
  ['google-calendar:create-event']: googleCalendarCreateEvent,
  ['google-calendar:delete-event']: googleCalendarDeleteEvent,
  ['google-calendar:list-calendars']: googleCalendarListCalendars,
  ['google-calendar:list-events']: googleCalendarListEvents,
  ['google-calendar:update-event']: googleCalendarUpdateEvent,
  ['google-docs:create-document']: googleDocsCreateDocument,
  ['google-docs:get-document']: googleDocsGetDocument,
  ['google-docs:update-document']: googleDocsUpdateDocument,
  ['google-drive:list-documents']: googleDriveListDocuments,
  ['google-drive:list-root-folders']: googleDriveListRootFolders,
  ['google-mail:compose-draft-reply']: googleMailComposeDraftReply,
  ['google-mail:compose-draft']: googleMailComposeDraft,
  ['google-mail:delete-message']: googleMailDeleteMessage,
  ['google-mail:get-message']: googleMailGetMessage,
  ['google-mail:list-messages']: googleMailListMessages,
  ['google-mail:modify-message-labels']: googleMailModifyMessageLabels,
  ['google-mail:search-messages']: googleMailSearchMessages,
  ['google-mail:send-email']: googleMailSendEmail,
  ['google-mail:trash-message']: googleMailTrashMessage,
  ['google-mail:untrash-message']: googleMailUntrashMessage,
  ['google-sheet:create-sheet']: googleSheetCreateSheet,
  ['google-sheet:update-sheet']: googleSheetUpdateSheet,
  ['harvest:add-historical-time-entry']: harvestAddHistoricalTimeEntry,
  ['harvest:create-client']: harvestCreateClient,
  ['harvest:create-project']: harvestCreateProject,
  ['harvest:delete-project']: harvestDeleteProject,
  ['harvest:delete-time-entry']: harvestDeleteTimeEntry,
  ['harvest:get-client']: harvestGetClient,
  ['harvest:get-project']: harvestGetProject,
  ['harvest:get-time-entry']: harvestGetTimeEntry,
  ['harvest:list-clients']: harvestListClients,
  ['harvest:list-project-tasks']: harvestListProjectTasks,
  ['harvest:list-projects']: harvestListProjects,
  ['harvest:list-tasks']: harvestListTasks,
  ['harvest:list-time-entries']: harvestListTimeEntries,
  ['harvest:restart-timer']: harvestRestartTimer,
  ['harvest:start-timer']: harvestStartTimer,
  ['harvest:stop-timer']: harvestStopTimer,
  ['harvest:update-time-entry']: harvestUpdateTimeEntry,
  ['linear:create-issue']: linearCreateIssue,
  ['linear:create-project']: linearCreateProject,
  ['linear:delete-issue']: linearDeleteIssue,
  ['linear:get-issue']: linearGetIssue,
  ['linear:get-project']: linearGetProject,
  ['linear:get-team']: linearGetTeam,
  ['linear:list-issues']: linearListIssues,
  ['linear:list-projects']: linearListProjects,
  ['linear:list-teams']: linearListTeams,
  ['linear:update-issue']: linearUpdateIssue,
  ['linear:update-project']: linearUpdateProject,
  ['linkedin:get-user-profile']: linkedinGetUserProfile,
  ['linkedin:send-post']: linkedinSendPost,
  ['notion:create-database']: notionCreateDatabase,
  ['notion:create-page']: notionCreatePage,
  ['notion:get-database']: notionGetDatabase,
  ['notion:get-page']: notionGetPage,
  ['notion:query-database']: notionQueryDatabase,
  ['notion:search']: notionSearch,
  ['notion:update-database']: notionUpdateDatabase,
  ['notion:update-page']: notionUpdatePage,
  ['slack:add-reaction-as-user']: slackAddReactionAsUser,
  ['slack:get-channel-history']: slackGetChannelHistory,
  ['slack:get-message-permalink']: slackGetMessagePermalink,
  ['slack:get-user-info']: slackGetUserInfo,
  ['slack:list-channels']: slackListChannels,
  ['slack:search-messages']: slackSearchMessages,
  ['slack:send-message-as-user']: slackSendMessageAsUser,
  ['slack:update-message-as-user']: slackUpdateMessageAsUser,
  ['twitter-v2:get-user-profile']: twitterV2GetUserProfile,
  ['twitter-v2:send-post']: twitterV2SendPost,
  ['x-social:get-user-profile']: xSocialGetUserProfile,
  ['x-social:send-post']: xSocialSendPost,
};

export function getRunner(providerKey: string, actionKey: string): Function | undefined {
  return actionMap[`${providerKey}:${actionKey}`];
}
