import type { NangoAction, NotionCreatePageInput, NotionPageOrDatabase } from '../models';

const NOTION_API_VERSION = '2022-06-28';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionCreatePageInput
): Promise<NotionPageOrDatabase | ErrorResponse> {
  const { parentId, parentType = 'page', properties, children } = input;

  if (!parentId) {
    return {
      error: {
        status: 400,
        message: 'Parent ID (parentId) is required. This can be a page ID or a database ID.',
      },
    };
  }
  if (!properties || typeof properties !== 'object' || Object.keys(properties).length === 0) {
    return {
      error: {
        status: 400,
        message: 'Page properties are required and must be a non-empty object.',
      },
    };
  }

  const requestBody: Record<string, any> = {
    parent: {},
    properties: properties,
  };

  if (parentType === 'database') {
    requestBody['parent'] = { database_id: parentId };
  } else if (parentType === 'page') {
    requestBody['parent'] = { page_id: parentId };
  } else {
    console.error(
      `Invalid or missing parentType provided: '${parentType}'. Defaulting to 'page_id'.`
    );
    requestBody['parent'] = { page_id: parentId };
  }

  if (children !== undefined) {
    requestBody['children'] = children;
  }

  try {
    const config = {
      method: 'POST' as const,
      endpoint: `/v1/pages`,
      data: requestBody,
      headers: {
        'Content-Type': 'application/json',
        'Notion-Version': NOTION_API_VERSION,
      },
      retries: 3,
    };

    const response = await nango.proxy<NotionPageOrDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message || error.message || 'Unknown error creating Notion page';
    const status = error.response?.status ?? 500;
    console.error(`Error creating Notion page: Status ${status}, Message: ${errorMessage}`);
    return { error: { status: status, message: errorMessage } };
  }
}
