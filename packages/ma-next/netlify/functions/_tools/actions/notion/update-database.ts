import type { NangoAction, NotionDatabase, NotionUpdateDatabaseInput } from '../models';

const NOTION_API_VERSION = '2022-06-28';

type NangoError = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: NotionUpdateDatabaseInput
): Promise<NotionDatabase | NangoError> {
  const { databaseId, title, description, properties, archived } = input;

  if (!databaseId) {
    return { error: { status: 400, message: 'Database ID (databaseId) is required.' } };
  }

  const requestBody: Record<string, unknown> = {};
  if (title !== undefined) {
    requestBody['title'] = title;
  }
  if (description !== undefined) {
    requestBody['description'] = description;
  }
  if (properties !== undefined) {
    requestBody['properties'] = properties;
  }
  if (archived !== undefined) {
    requestBody['archived'] = archived;
  }

  if (Object.keys(requestBody).length === 0) {
    return {
      error: {
        status: 400,
        message:
          'At least one property (e.g. title, description, properties, archived) must be provided to update the database.',
      },
    };
  }

  try {
    const config = {
      method: 'PATCH' as const,
      endpoint: `/v1/databases/${databaseId}`,
      headers: {
        'Notion-Version': NOTION_API_VERSION,
        'Content-Type': 'application/json',
      },
      data: requestBody,
      retries: 3,
    };

    const response = await nango.proxy<NotionDatabase>(config);

    return response.data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Unknown error updating Notion database ${databaseId}`;
    const status = error.response?.status ?? 500;
    console.error(
      `Error updating Notion database ${databaseId}: Status ${status}, Message: ${errorMessage}`
    );
    return { error: { status: status, message: errorMessage } };
  }
}
