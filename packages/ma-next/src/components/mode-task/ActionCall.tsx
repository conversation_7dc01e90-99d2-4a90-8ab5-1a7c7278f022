import { ToolInvocationPart, ActionCallInvocation } from 'chat/protocol/ui';
import { ConnectProviderForAction } from 'components/setup/ConnectProviderForAction';
import { AuthForAction } from 'components/setup/AuthForAction';
import { useConnections } from 'hooks/useConnections';
import { useAuth } from 'hooks/useAuth';
import { ActionCard } from './ActionCard';
import { MiniActionCallCard } from './MiniActionCallCard';
import { RichResultDisplay } from 'components/rich-ui/RichResultDisplay';

type ActionCallProps = {
  part: ToolInvocationPart & { content: ActionCallInvocation };
  isLatestMessage: boolean;
};

const ActionCall = ({ part, isLatestMessage }: ActionCallProps) => {
  const { connectionsByProviderKey } = useConnections();
  const { isAuthenticated } = useAuth();
  const { args, toolCallId, result, connectionRequired, confirmationRequired, autoApproved } =
    part.content;

  // This situation is really bad an caused by LLM failing to call with correct params, but annoying to fix. Currently user will see flicker, and have to re-submit.
  if (typeof result === 'object' && 'error' in result && !('success' in result)) {
    return null;
  }

  const hasError = result && typeof result === 'object' && 'error' in result;

  if (((confirmationRequired && !autoApproved && !result) || connectionRequired) && !hasError) {
    const showConnect =
      connectionRequired &&
      isAuthenticated &&
      !connectionsByProviderKey[args?.providerKey] &&
      (!result || !isLatestMessage);

    const showAuth = connectionRequired && !isAuthenticated && (!result || !isLatestMessage);
    const isCancelled = (!isLatestMessage && !result) || result?.userCancelled;

    return (
      <div className="my-4">
        {showAuth && <AuthForAction id={toolCallId} />}

        {showConnect && args?.providerKey && (
          <ConnectProviderForAction providerKey={args.providerKey} id={toolCallId} />
        )}

        <ActionCard
          toolCallId={toolCallId}
          actionConfig={args}
          hideHeader={showConnect || showAuth}
          disabled={(!isAuthenticated || !connectionsByProviderKey[args?.providerKey]) && !result}
          result={result}
          isCancelled={isCancelled}
        />
        {!isCancelled && result?.success && args.providerKey && args.actionKey && (
          <RichResultDisplay
            result={result.data}
            providerKey={args.providerKey}
            actionKey={args.actionKey}
          />
        )}
      </div>
    );
  } else {
    if (result?.userCancelled) {
      return (
        <MiniActionCallCard
          type="cancelled"
          args={args}
          toolCallId={toolCallId}
          result={result}
          isLatestMessage={isLatestMessage}
        />
      );
    } else if (hasError) {
      return (
        <MiniActionCallCard
          type="error"
          args={args}
          toolCallId={toolCallId}
          result={result}
          isLatestMessage={isLatestMessage}
        />
      );
    } else if (result) {
      return (
        <>
          <MiniActionCallCard
            type={autoApproved ? 'auto-approved' : 'success'}
            args={args}
            toolCallId={toolCallId}
            result={result}
            isLatestMessage={isLatestMessage}
            autoApproved={autoApproved}
          />
          {result?.success && args.providerKey && args.actionKey && (
            <RichResultDisplay
              result={result.data}
              providerKey={args.providerKey}
              actionKey={args.actionKey}
            />
          )}
        </>
      );
    } else {
      return (
        <MiniActionCallCard
          type="working"
          args={args}
          toolCallId={toolCallId}
          isLatestMessage={isLatestMessage}
        />
      );
    }
  }
};

export { ActionCall };
