import { GmailMessageDisplay } from './rich-results/GmailMessageDisplay';
import { GmailSingleMessageDisplay } from './rich-results/GmailSingleMessageDisplay';
import { GmailDraftOutputDisplay } from './rich-results/GmailDraftOutputDisplay';
import { GmailSendEmailOutputDisplay } from './rich-results/GmailSendEmailOutputDisplay';
import { GmailEmailDisplay } from './rich-results/GmailEmailDisplay';
import { GoogleCalendarEventDisplay } from './rich-results/GoogleCalendarEventDisplay';
import { GoogleCalendarEventOutputDisplay } from './rich-results/GoogleCalendarEventOutputDisplay';
import { GoogleCalendarListDisplay } from './rich-results/GoogleCalendarListDisplay';
import { GoogleDocsCreateDocumentDisplay } from './rich-results/GoogleDocsCreateDocumentDisplay';
import { GoogleDocsFetchDocumentDisplay } from './rich-results/GoogleDocsFetchDocumentDisplay';
import { GoogleDocsGetDocumentDisplay } from './rich-results/GoogleDocsGetDocumentDisplay';
import { GoogleDocsUpdateDocumentDisplay } from './rich-results/GoogleDocsUpdateDocumentDisplay';
import { GoogleSheetCreateSheetDisplay } from './rich-results/GoogleSheetCreateSheetDisplay';
import { GoogleSheetFetchSpreadsheetDisplay } from './rich-results/GoogleSheetFetchSpreadsheetDisplay';
import { GoogleSheetUpdateSheetDisplay } from './rich-results/GoogleSheetUpdateSheetDisplay';
import { GoogleDriveDocumentListDisplay } from './rich-results/GoogleDriveDocumentListDisplay';
import { GoogleDriveFolderListDisplay } from './rich-results/GoogleDriveFolderListDisplay';
import { FolderContentDisplay } from './rich-results/FolderContentDisplay';
import { GoogleDriveFetchDocumentDisplay } from './rich-results/GoogleDriveFetchDocumentDisplay';
import { GoogleDriveFetchGoogleDocDisplay } from './rich-results/GoogleDriveFetchGoogleDocDisplay';
import { GoogleDriveFetchGoogleSheetDisplay } from './rich-results/GoogleDriveFetchGoogleSheetDisplay';
import { GoogleDriveUploadDocumentDisplay } from './rich-results/GoogleDriveUploadDocumentDisplay';
import { SlackConversationListDisplay } from './rich-results/SlackConversationListDisplay';
import { SlackMessageListDisplay } from './rich-results/SlackMessageListDisplay';
import { SlackSendMessageResultDisplay } from './rich-results/SlackSendMessageResultDisplay';
import { SlackUserInfoDisplay } from './rich-results/SlackUserInfoDisplay';
import { SlackUpdateMessageOutputDisplay } from './rich-results/SlackUpdateMessageOutputDisplay';
import { DropboxFolderDisplay } from './rich-results/DropboxFolderDisplay';
import { DropboxFileDisplay } from './rich-results/DropboxFileDisplay';
import { DropboxFileListDisplay } from './rich-results/DropboxFileListDisplay';
import { DropboxSearchResultDisplay } from './rich-results/DropboxSearchResultDisplay';
import { NotionPageOrDatabaseDisplay } from './rich-results/NotionPageOrDatabaseDisplay';
import { NotionSearchResultDisplay } from './rich-results/NotionSearchResultDisplay';
import { XSocialUserProfileDisplay } from './rich-results/XSocialUserProfileDisplay';
import { XSocialPostOutputDisplay } from './rich-results/XSocialPostOutputDisplay';
import { LinkedInUserProfileDisplay } from './rich-results/LinkedInUserProfileDisplay';

const RICH_RESULT_MODEL_COMPONENTS: Record<string, React.FC<{ output: any }>> = {
  GmailMessageList: GmailMessageDisplay,
  GmailMessage: GmailSingleMessageDisplay,
  GmailEmail: GmailEmailDisplay,
  GmailDraftOutput: GmailDraftOutputDisplay,
  GmailSendEmailOutput: GmailSendEmailOutputDisplay,
  GoogleCalendarEventList: GoogleCalendarEventDisplay,
  GoogleCalendarEventOutput: GoogleCalendarEventOutputDisplay,
  GoogleCalendarList: GoogleCalendarListDisplay,
  GoogleDocsDocument: GoogleDocsCreateDocumentDisplay,
  Document: GoogleDocsFetchDocumentDisplay,
  GoogleDocsUpdateDocumentOutput: GoogleDocsUpdateDocumentDisplay,
  GoogleSheetCreateOutput: GoogleSheetCreateSheetDisplay,
  Spreadsheet: GoogleSheetFetchSpreadsheetDisplay,
  GoogleSheetUpdateOutput: GoogleSheetUpdateSheetDisplay,
  GoogleDriveDocumentList: GoogleDriveDocumentListDisplay,
  GoogleDriveFolderList: GoogleDriveFolderListDisplay,
  FolderContent: FolderContentDisplay,
  Anonymous_googledrive_action_fetchdocument_output: GoogleDriveFetchDocumentDisplay,
  JSONDocument: GoogleDriveFetchGoogleDocDisplay,
  JSONSpreadsheet: GoogleDriveFetchGoogleSheetDisplay,
  GoogleDocument: GoogleDriveUploadDocumentDisplay,
  SlackConversationsList: SlackConversationListDisplay,
  SlackMessageList: SlackMessageListDisplay,
  SlackSendMessageOutput: SlackSendMessageResultDisplay,
  SlackUserInfo: SlackUserInfoDisplay,
  SlackUpdateMessageOutput: SlackUpdateMessageOutputDisplay,
  DropboxFolder: DropboxFolderDisplay,
  DropboxFile: DropboxFileDisplay,
  DropboxFileList: DropboxFileListDisplay,
  DropboxSearchResult: DropboxSearchResultDisplay,
  NotionPageOrDatabase: NotionPageOrDatabaseDisplay,
  NotionSearchOutput: NotionSearchResultDisplay,
  XSocialUserProfile: XSocialUserProfileDisplay,
  XSocialPostOutput: XSocialPostOutputDisplay,
  LinkedInUserProfile: LinkedInUserProfileDisplay,
};

interface RichResultModelDisplayProps {
  modelName: string;
  result?: any;
}

function RichResultModelDisplay({ modelName, result }: RichResultModelDisplayProps) {
  const DisplayComponent = RICH_RESULT_MODEL_COMPONENTS[modelName];
  if (!DisplayComponent) return null;
  return (
    <div className="mb-8 -mt-2 rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <DisplayComponent output={result} />
    </div>
  );
}

RichResultModelDisplay.canDisplay = (modelName: string) => {
  return Boolean(RICH_RESULT_MODEL_COMPONENTS[modelName]);
};

export { RichResultModelDisplay };

