import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>ch,
  Shield,
  <PERSON><PERSON>heck,
  Link,
  Copy
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubBranchList } from 'src/config/nangoModels';

type GithubBranchListDisplayProps = {
  output: GithubBranchList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub branch list
 */
function GithubBranchListDisplay({ output, actionParameters }: GithubBranchListDisplayProps) {
  if (!output || !output.branches || output.branches.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No branches found</p>
      </div>
    );
  }

  const protectedBranches = output.branches.filter(branch => branch.protected).length;
  const unprotectedBranches = output.branches.length - protectedBranches;

  // Extract repository info from action parameters
  const repositoryName = actionParameters?.owner && actionParameters?.repo
    ? `${actionParameters.owner}/${actionParameters.repo}`
    : null;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {repositoryName ? `${repositoryName} Branches` : 'Repository Branches'}
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            <span className="text-gray-600 dark:text-gray-300">
              {output.branches.length} total
            </span>
            {protectedBranches > 0 && (
              <span className="text-green-600 dark:text-green-400">
                {protectedBranches} protected
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.branches.map((branch, index) => {
          return (
            <div key={branch.name || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center min-w-0 flex-1">
                  <GitBranch className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-3 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {branch.name}
                      </h4>
                      {branch.protected && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                          <ShieldCheck className="w-3 h-3 mr-1" />
                          Protected
                        </span>
                      )}
                    </div>

                    {/* Commit info */}
                    {branch.commit && (
                      <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {branch.commit.sha && (
                          <span className="font-mono">
                            {branch.commit.sha.substring(0, 7)}
                          </span>
                        )}
                        {branch.commit.commit && branch.commit.commit.message && (
                          <span className="ml-2 truncate">
                            {branch.commit.commit.message.split('\n')[0]}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => navigator.clipboard.writeText(branch.name)}
                    className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    title="Copy branch name"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                  {branch.protection_url && (
                    <a
                      href={branch.protection_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      title="View protection settings"
                    >
                      <Shield className="w-3 h-3" />
                    </a>
                  )}
                </div>
              </div>

              {/* Protection details */}
              {branch.protection && Object.keys(branch.protection).length > 0 && (
                <div className="mt-2 ml-7">
                  <div className="text-xs text-gray-500 dark:text-gray-400 p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center">
                      <Shield className="w-3 h-3 mr-1" />
                      <span>Branch protection rules active</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubBranchListDisplay };
