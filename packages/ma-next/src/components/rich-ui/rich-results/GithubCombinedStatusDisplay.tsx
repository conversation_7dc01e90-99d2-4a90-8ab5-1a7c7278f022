import React from 'react';
import { 
  <PERSON><PERSON>ircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubCombinedStatus } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubCombinedStatusDisplayProps = {
  output: GithubCombinedStatus;
};

/**
 * Renders a rich display of GitHub combined status (CI/CD checks)
 */
function GithubCombinedStatusDisplay({ output }: GithubCombinedStatusDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No status data available</p>
      </div>
    );
  }

  const getOverallStatusIcon = () => {
    switch (output.state) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'failure':
      case 'error':
        return <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getOverallStatusColor = () => {
    switch (output.state) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failure':
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'failure':
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Status Checks
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getOverallStatusColor()}`}>
              {getOverallStatusIcon()}
              <span className="ml-1 capitalize">{output.state}</span>
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Commit info */}
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-300">
              <span className="font-medium">Commit:</span>
              <span className="ml-2 font-mono text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                {output.sha.substring(0, 7)}
              </span>
            </div>
            {output.commit_url && (
              <a
                href={output.commit_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                <Link className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>

        {/* Repository info */}
        {output.repository && (
          <div className="mb-4 text-sm text-gray-600 dark:text-gray-300">
            <span className="font-medium">Repository:</span>
            <span className="ml-2">{output.repository.full_name}</span>
          </div>
        )}

        {/* Status summary */}
        <div className="mb-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status Summary ({output.total_count} checks)
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['success', 'failure', 'error', 'pending'].map(state => {
              const count = output.statuses.filter(status => status.state === state).length;
              if (count === 0) return null;
              
              return (
                <div key={state} className="flex items-center">
                  {getStatusIcon(state)}
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-300">
                    {count} {state}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Individual status checks */}
        {output.statuses && output.statuses.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Individual Checks
            </div>
            <div className="space-y-3">
              {output.statuses.map((status, index) => {
                let formattedDate = '';
                try {
                  if (status.updated_at) {
                    formattedDate = format(new Date(status.updated_at), 'MMM d, HH:mm');
                  }
                } catch (e) {
                  formattedDate = status.updated_at || '';
                }

                return (
                  <div key={status.id || index} className="flex items-start p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
                    <div className="flex-shrink-0 mr-3 mt-0.5">
                      {getStatusIcon(status.state)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {status.context}
                        </h4>
                        {status.target_url && (
                          <a
                            href={status.target_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                          >
                            <ExternalLink className="w-3 h-3" />
                          </a>
                        )}
                      </div>
                      {status.description && (
                        <p className="text-xs text-gray-600 dark:text-gray-300 mb-1">
                          {status.description}
                        </p>
                      )}
                      {formattedDate && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formattedDate}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export { GithubCombinedStatusDisplay };
