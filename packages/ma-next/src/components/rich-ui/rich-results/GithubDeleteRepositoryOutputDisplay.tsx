import React from 'react';
import { 
  Trash2, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  Alert<PERSON>riangle
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubDeleteRepositoryOutput } from 'src/config/nangoModels';

type GithubDeleteRepositoryOutputDisplayProps = {
  output: GithubDeleteRepositoryOutput;
};

/**
 * Renders a rich display of a GitHub repository deletion result
 */
function GithubDeleteRepositoryOutputDisplay({ output }: GithubDeleteRepositoryOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No deletion result available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Repository Deletion Result
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.success ? (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <CheckCircle className="w-3 h-3 mr-1" />
                Success
              </span>
            ) : (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <XCircle className="w-3 h-3 mr-1" />
                Failed
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Deletion status */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {output.success ? 'Repository Successfully Deleted' : 'Repository Deletion Failed'}
            </h4>
          </div>
        </div>

        {/* Message */}
        {output.message && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Details
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              {output.message}
            </div>
          </div>
        )}

        {/* Warning or success info */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {output.success ? (
            <div className="flex items-center">
              <CheckCircle className="w-3 h-3 mr-1 text-green-600 dark:text-green-400" />
              The repository has been permanently deleted and cannot be recovered.
            </div>
          ) : (
            <div className="flex items-center">
              <AlertTriangle className="w-3 h-3 mr-1 text-red-600 dark:text-red-400" />
              The repository deletion could not be completed. Check permissions and try again.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubDeleteRepositoryOutputDisplay };
