import React from 'react';
import {
  AlertCircle,
  CheckCircle,
  User,
  Calendar,
  MessageSquare,
  Tag,
  Link
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubIssueList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubIssueListDisplayProps = {
  output: GithubIssueList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub issue list
 */
function GithubIssueListDisplay({ output, actionParameters }: GithubIssueListDisplayProps) {
  if (!output || !output.issues || output.issues.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No issues found</p>
      </div>
    );
  }

  const openIssues = output.issues.filter(issue => issue.state === 'open').length;
  const closedIssues = output.issues.length - openIssues;

  // Extract repository info from action parameters
  const repositoryName = actionParameters?.owner && actionParameters?.repo
    ? `${actionParameters.owner}/${actionParameters.repo}`
    : null;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {repositoryName ? `${repositoryName} Issues` : 'GitHub Issues'}
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            <span className="text-green-600 dark:text-green-400">
              {openIssues} open
            </span>
            <span className="text-purple-600 dark:text-purple-400">
              {closedIssues} closed
            </span>
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.issues.map((issue, index) => {
          // Format dates
          let formattedCreatedAt = '';
          try {
            if (issue.created_at) {
              formattedCreatedAt = format(new Date(issue.created_at), 'MMM d');
            }
          } catch (e) {
            formattedCreatedAt = issue.created_at || '';
          }

          const isOpen = issue.state === 'open';

          return (
            <div key={issue.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* Issue header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    {isOpen ? (
                      <AlertCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                    ) : (
                      <CheckCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {issue.title}
                      </h4>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                        #{issue.number}
                      </span>
                      {issue.html_url && (
                        <a
                          href={issue.html_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                        >
                          <Link className="w-3 h-3" />
                        </a>
                      )}
                    </div>

                    {/* Issue metadata */}
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                      {issue.user && (
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          {issue.user.login}
                        </div>
                      )}
                      {formattedCreatedAt && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formattedCreatedAt}
                        </div>
                      )}
                      {issue.comments > 0 && (
                        <div className="flex items-center">
                          <MessageSquare className="w-3 h-3 mr-1" />
                          {issue.comments}
                        </div>
                      )}
                    </div>

                    {/* Labels */}
                    {issue.labels && issue.labels.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {issue.labels.slice(0, 3).map((label, labelIndex) => (
                          <span
                            key={label.id || labelIndex}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                            style={{
                              backgroundColor: `#${label.color}20`,
                              color: `#${label.color}`,
                              borderColor: `#${label.color}40`,
                              border: '1px solid'
                            }}
                          >
                            {label.name}
                          </span>
                        ))}
                        {issue.labels.length > 3 && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            +{issue.labels.length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubIssueListDisplay };
