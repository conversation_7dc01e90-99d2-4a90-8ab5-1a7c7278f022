import React from 'react';
import { 
  MessageSquare, 
  User, 
  Calendar,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequestComment } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubPullRequestCommentDisplayProps = {
  output: GithubPullRequestComment;
};

/**
 * Renders a rich display of a single GitHub pull request comment
 */
function GithubPullRequestCommentDisplay({ output }: GithubPullRequestCommentDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No comment data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.created_at) {
      formattedCreatedAt = format(new Date(output.created_at), 'MMM d, yyyy HH:mm');
    }
    if (output.updated_at && output.updated_at !== output.created_at) {
      formattedUpdatedAt = format(new Date(output.updated_at), 'MMM d, yyyy HH:mm');
    }
  } catch (e) {
    formattedCreatedAt = output.created_at || '';
    formattedUpdatedAt = output.updated_at || '';
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Pull Request Comment
          </h3>
          {output.html_url && (
            <a
              href={output.html_url}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-auto text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>

      <div className="p-4">
        {/* Comment header */}
        <div className="flex items-center mb-4">
          <MessageSquare className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-2" />
          {output.user && (
            <div className="flex items-center">
              <User className="w-3 h-3 text-gray-500 mr-1" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {output.user.login}
              </span>
            </div>
          )}
          {formattedCreatedAt && (
            <div className="flex items-center ml-4">
              <Calendar className="w-3 h-3 text-gray-500 mr-1" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formattedCreatedAt}
              </span>
            </div>
          )}
        </div>

        {/* Comment body */}
        {output.body && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="whitespace-pre-line">
                {output.body}
              </div>
            </div>
          </div>
        )}

        {/* Updated timestamp */}
        {formattedUpdatedAt && (
          <div className="mb-3 text-xs text-gray-500 dark:text-gray-400">
            Updated {formattedUpdatedAt}
          </div>
        )}

        {/* Author association */}
        {output.author_association && output.author_association !== 'NONE' && (
          <div className="mb-3">
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
              {output.author_association.toLowerCase().replace('_', ' ')}
            </span>
          </div>
        )}

        {/* Comment ID */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Comment ID: {output.id}
        </div>
      </div>
    </div>
  );
}

export { GithubPullRequestCommentDisplay };
