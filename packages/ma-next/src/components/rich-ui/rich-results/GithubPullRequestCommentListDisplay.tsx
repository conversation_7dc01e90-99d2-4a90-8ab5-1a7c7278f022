import React from 'react';
import { 
  MessageSquare, 
  User, 
  Calendar,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequestCommentList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubPullRequestCommentListDisplayProps = {
  output: GithubPullRequestCommentList;
};

/**
 * Renders a rich display of GitHub pull request comments
 */
function GithubPullRequestCommentListDisplay({ output }: GithubPullRequestCommentListDisplayProps) {
  if (!output || !output.comments || output.comments.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No comments found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Pull Request Comments
          </h3>
          <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
            {output.comments.length} comments
          </span>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.comments.map((comment, index) => {
          // Format dates
          let formattedCreatedAt = '';
          let formattedUpdatedAt = '';
          try {
            if (comment.created_at) {
              formattedCreatedAt = format(new Date(comment.created_at), 'MMM d, yyyy HH:mm');
            }
            if (comment.updated_at && comment.updated_at !== comment.created_at) {
              formattedUpdatedAt = format(new Date(comment.updated_at), 'MMM d, yyyy HH:mm');
            }
          } catch (e) {
            formattedCreatedAt = comment.created_at || '';
            formattedUpdatedAt = comment.updated_at || '';
          }

          return (
            <div key={comment.id || index} className="p-4">
              {/* Comment header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <MessageSquare className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-2" />
                  {comment.user && (
                    <div className="flex items-center">
                      <User className="w-3 h-3 text-gray-500 mr-1" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {comment.user.login}
                      </span>
                    </div>
                  )}
                  {formattedCreatedAt && (
                    <div className="flex items-center ml-3">
                      <Calendar className="w-3 h-3 text-gray-500 mr-1" />
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formattedCreatedAt}
                      </span>
                    </div>
                  )}
                </div>
                {comment.html_url && (
                  <a
                    href={comment.html_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                  >
                    <ExternalLink className="w-3 h-3" />
                  </a>
                )}
              </div>

              {/* Comment body */}
              {comment.body && (
                <div className="mb-3">
                  <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
                    <div className="whitespace-pre-line">
                      {comment.body}
                    </div>
                  </div>
                </div>
              )}

              {/* Updated timestamp */}
              {formattedUpdatedAt && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Updated {formattedUpdatedAt}
                </div>
              )}

              {/* Author association */}
              {comment.author_association && comment.author_association !== 'NONE' && (
                <div className="mt-2">
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    {comment.author_association.toLowerCase().replace('_', ' ')}
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubPullRequestCommentListDisplay };
