import React from 'react';
import { 
  FileText, 
  Plus, 
  Minus, 
  Edit,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequestFileList } from 'src/config/nangoModels';

type GithubPullRequestFileListDisplayProps = {
  output: GithubPullRequestFileList;
};

/**
 * Renders a rich display of GitHub pull request files
 */
function GithubPullRequestFileListDisplay({ output }: GithubPullRequestFileListDisplayProps) {
  if (!output || !output.files || output.files.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No files found</p>
      </div>
    );
  }

  const totalAdditions = output.files.reduce((sum, file) => sum + (file.additions || 0), 0);
  const totalDeletions = output.files.reduce((sum, file) => sum + (file.deletions || 0), 0);
  const totalChanges = output.files.reduce((sum, file) => sum + (file.changes || 0), 0);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'added':
        return <Plus className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'removed':
        return <Minus className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'modified':
        return <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      default:
        return <FileText className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'added':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'removed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'modified':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Changed Files
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            <span className="text-gray-600 dark:text-gray-300">
              {output.files.length} files
            </span>
            <span className="text-green-600 dark:text-green-400">
              +{totalAdditions}
            </span>
            <span className="text-red-600 dark:text-red-400">
              -{totalDeletions}
            </span>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="p-4 bg-gray-50 dark:bg-gray-900/30 border-b border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-green-600 dark:text-green-400">
              +{totalAdditions}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">additions</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              -{totalDeletions}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">deletions</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-600 dark:text-gray-300">
              {totalChanges}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">total changes</div>
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.files.map((file, index) => {
          return (
            <div key={file.sha || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* File header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3">
                    {getStatusIcon(file.status)}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {file.filename}
                      </h4>
                      <span className={`ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(file.status)}`}>
                        {file.status}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  {file.blob_url && (
                    <a
                      href={file.blob_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                      title="View file"
                    >
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                </div>
              </div>

              {/* File stats */}
              <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400 mb-2">
                {file.additions > 0 && (
                  <div className="flex items-center">
                    <Plus className="w-3 h-3 text-green-600 dark:text-green-400 mr-1" />
                    <span className="text-green-600 dark:text-green-400">
                      {file.additions} additions
                    </span>
                  </div>
                )}
                {file.deletions > 0 && (
                  <div className="flex items-center">
                    <Minus className="w-3 h-3 text-red-600 dark:text-red-400 mr-1" />
                    <span className="text-red-600 dark:text-red-400">
                      {file.deletions} deletions
                    </span>
                  </div>
                )}
                {file.changes > 0 && (
                  <span>{file.changes} changes</span>
                )}
              </div>

              {/* Additional links */}
              <div className="flex flex-wrap gap-2 text-xs">
                {file.raw_url && (
                  <a
                    href={file.raw_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                  >
                    Raw
                  </a>
                )}
                {file.contents_url && (
                  <a
                    href={file.contents_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                  >
                    Contents
                  </a>
                )}
              </div>

              {/* Patch preview (if available and not too long) */}
              {file.patch && file.patch.length < 1000 && (
                <div className="mt-3">
                  <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Patch Preview:
                  </div>
                  <div className="text-xs font-mono text-gray-600 dark:text-gray-300 p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700 overflow-x-auto">
                    <pre className="whitespace-pre-wrap">{file.patch}</pre>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubPullRequestFileListDisplay };
