import React from 'react';
import {
  GitPullRequest,
  GitMerge,
  User,
  Calendar,
  MessageSquare,
  Link,
  XCircle
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequestList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubPullRequestListDisplayProps = {
  output: GithubPullRequestList;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a GitHub pull request list
 */
function GithubPullRequestListDisplay({ output, actionParameters }: GithubPullRequestListDisplayProps) {
  if (!output || !output.pull_requests || output.pull_requests.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No pull requests found</p>
      </div>
    );
  }

  const openPRs = output.pull_requests.filter(pr => pr.state === 'open' && !pr.merged).length;
  const mergedPRs = output.pull_requests.filter(pr => pr.merged).length;
  const closedPRs = output.pull_requests.filter(pr => pr.state === 'closed' && !pr.merged).length;

  // Extract repository info from action parameters
  const repositoryName = actionParameters?.owner && actionParameters?.repo
    ? `${actionParameters.owner}/${actionParameters.repo}`
    : null;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {repositoryName ? `${repositoryName} Pull Requests` : 'Pull Requests'}
          </h3>
          <div className="ml-auto flex items-center space-x-3 text-xs">
            <span className="text-green-600 dark:text-green-400">
              {openPRs} open
            </span>
            <span className="text-purple-600 dark:text-purple-400">
              {mergedPRs} merged
            </span>
            <span className="text-red-600 dark:text-red-400">
              {closedPRs} closed
            </span>
          </div>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.pull_requests.map((pr, index) => {
          // Format dates
          let formattedCreatedAt = '';
          try {
            if (pr.created_at) {
              formattedCreatedAt = format(new Date(pr.created_at), 'MMM d');
            }
          } catch (e) {
            formattedCreatedAt = pr.created_at || '';
          }

          const getStatusIcon = () => {
            if (pr.merged) {
              return <GitMerge className="w-4 h-4 text-purple-600 dark:text-purple-400" />;
            } else if (pr.state === 'closed') {
              return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
            } else {
              return <GitPullRequest className="w-4 h-4 text-green-600 dark:text-green-400" />;
            }
          };

          return (
            <div key={pr.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* PR header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start min-w-0 flex-1">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    {getStatusIcon()}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center mb-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {pr.title}
                      </h4>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
                        #{pr.number}
                      </span>
                      {pr.html_url && (
                        <a
                          href={pr.html_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                        >
                          <Link className="w-3 h-3" />
                        </a>
                      )}
                    </div>

                    {/* Branch info */}
                    {pr.head && pr.base && (
                      <div className="mb-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className="font-mono bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 px-1.5 py-0.5 rounded">
                          {pr.head.ref}
                        </span>
                        <span className="mx-1">→</span>
                        <span className="font-mono bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300 px-1.5 py-0.5 rounded">
                          {pr.base.ref}
                        </span>
                      </div>
                    )}

                    {/* PR metadata */}
                    <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                      {pr.user && (
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          {pr.user.login}
                        </div>
                      )}
                      {formattedCreatedAt && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {formattedCreatedAt}
                        </div>
                      )}
                      {pr.comments > 0 && (
                        <div className="flex items-center">
                          <MessageSquare className="w-3 h-3 mr-1" />
                          {pr.comments}
                        </div>
                      )}
                      {pr.commits > 0 && (
                        <span>{pr.commits} commits</span>
                      )}
                      {pr.changed_files > 0 && (
                        <span>{pr.changed_files} files</span>
                      )}
                      {(pr.additions > 0 || pr.deletions > 0) && (
                        <span className="text-green-600 dark:text-green-400">
                          +{pr.additions || 0}
                        </span>
                      )}
                      {pr.deletions > 0 && (
                        <span className="text-red-600 dark:text-red-400">
                          -{pr.deletions}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubPullRequestListDisplay };
