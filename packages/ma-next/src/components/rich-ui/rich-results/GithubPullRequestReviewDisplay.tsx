import React from 'react';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  User, 
  Calendar,
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubPullRequestReview } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubPullRequestReviewDisplayProps = {
  output: GithubPullRequestReview;
};

/**
 * Renders a rich display of a GitHub pull request review
 */
function GithubPullRequestReviewDisplay({ output }: GithubPullRequestReviewDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No review data available</p>
      </div>
    );
  }

  // Format date
  let formattedSubmittedAt = '';
  try {
    if (output.submitted_at) {
      formattedSubmittedAt = format(new Date(output.submitted_at), 'MMM d, yyyy HH:mm');
    }
  } catch (e) {
    formattedSubmittedAt = output.submitted_at || '';
  }

  const getReviewIcon = () => {
    switch (output.state?.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'changes_requested':
        return <XCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'commented':
        return <MessageSquare className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      default:
        return <Eye className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getReviewColor = () => {
    switch (output.state?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'changes_requested':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'commented':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getReviewText = () => {
    switch (output.state?.toLowerCase()) {
      case 'approved':
        return 'Approved';
      case 'changes_requested':
        return 'Changes Requested';
      case 'commented':
        return 'Commented';
      default:
        return output.state || 'Review';
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Pull Request Review
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getReviewColor()}`}>
              {getReviewIcon()}
              <span className="ml-1">{getReviewText()}</span>
            </span>
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Review header */}
        <div className="flex items-center mb-4">
          {getReviewIcon()}
          {output.user && (
            <div className="flex items-center ml-3">
              <User className="w-3 h-3 text-gray-500 mr-1" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {output.user.login}
              </span>
            </div>
          )}
          {formattedSubmittedAt && (
            <div className="flex items-center ml-4">
              <Calendar className="w-3 h-3 text-gray-500 mr-1" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {formattedSubmittedAt}
              </span>
            </div>
          )}
          {output.html_url && (
            <a
              href={output.html_url}
              target="_blank"
              rel="noopener noreferrer"
              className="ml-auto text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
            >
              <ExternalLink className="w-4 h-4" />
            </a>
          )}
        </div>

        {/* Review body */}
        {output.body && (
          <div className="mb-4">
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="whitespace-pre-line">
                {output.body}
              </div>
            </div>
          </div>
        )}

        {/* Commit info */}
        {output.commit_id && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Reviewed Commit
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              {output.commit_id.substring(0, 7)}
            </div>
          </div>
        )}

        {/* Author association */}
        {output.author_association && output.author_association !== 'NONE' && (
          <div className="mb-3">
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
              {output.author_association.toLowerCase().replace('_', ' ')}
            </span>
          </div>
        )}

        {/* Review ID */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Review ID: {output.id}
        </div>
      </div>
    </div>
  );
}

export { GithubPullRequestReviewDisplay };
