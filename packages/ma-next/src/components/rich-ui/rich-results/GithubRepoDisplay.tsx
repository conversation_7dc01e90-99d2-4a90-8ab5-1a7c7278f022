import React from 'react';
import { 
  GitBranch, 
  Calendar, 
  Link,
  ExternalLink
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubRepo } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubRepoDisplayProps = {
  output: GithubRepo;
};

/**
 * Renders a rich display of a GitHub repo (simplified repository model)
 */
function GithubRepoDisplay({ output }: GithubRepoDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No repository data available</p>
      </div>
    );
  }

  // Format dates
  let formattedCreatedAt = '';
  let formattedUpdatedAt = '';
  try {
    if (output.date_created) {
      formattedCreatedAt = format(new Date(output.date_created), 'MMM d, yyyy');
    }
    if (output.date_last_modified) {
      formattedUpdatedAt = format(new Date(output.date_last_modified), 'MMM d, yyyy');
    }
  } catch (e) {
    formattedCreatedAt = output.date_created || '';
    formattedUpdatedAt = output.date_last_modified || '';
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            GitHub Repository
          </h3>
        </div>
      </div>

      <div className="p-4">
        {/* Repository name and description */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              {output.full_name}
            </h4>
            {output.url && (
              <a
                href={output.url}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
          {output.description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
              {output.description}
            </p>
          )}
        </div>

        {/* Repository details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Owner
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {output.owner}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Repository Name
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {output.name}
            </div>
          </div>
        </div>

        {/* Repository ID */}
        <div className="mb-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Repository ID
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 font-mono">
            {output.id}
          </div>
        </div>

        {/* Dates */}
        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
          {formattedCreatedAt && (
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              Created {formattedCreatedAt}
            </div>
          )}
          {formattedUpdatedAt && (
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              Updated {formattedUpdatedAt}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export { GithubRepoDisplay };
