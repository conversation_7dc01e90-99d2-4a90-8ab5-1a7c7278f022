import React from 'react';
import { 
  GitBranch, 
  Star, 
  GitFork, 
  Eye, 
  Lock, 
  Globe, 
  Calendar, 
  Code,
  AlertCircle,
  Link
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubRepositoryList } from 'src/config/nangoModels';
import { format } from 'date-fns';

type GithubRepositoryListDisplayProps = {
  output: GithubRepositoryList;
};

/**
 * Renders a rich display of a GitHub repository list
 */
function GithubRepositoryListDisplay({ output }: GithubRepositoryListDisplayProps) {
  if (!output || !output.repositories || output.repositories.length === 0) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No repositories found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            GitHub Repositories
          </h3>
          <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
            {output.repositories.length} repositories
          </span>
        </div>
        {output.note && (
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-2">
            {output.note}
          </p>
        )}
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {output.repositories.map((repo, index) => {
          // Format dates
          let formattedUpdatedAt = '';
          try {
            if (repo.updated_at) {
              formattedUpdatedAt = format(new Date(repo.updated_at), 'MMM d, yyyy');
            }
          } catch (e) {
            formattedUpdatedAt = repo.updated_at || '';
          }

          return (
            <div key={repo.id || index} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              {/* Repository header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center min-w-0 flex-1">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                    {repo.full_name}
                  </h4>
                  {repo.html_url && (
                    <a
                      href={repo.html_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex-shrink-0"
                    >
                      <Link className="w-3 h-3" />
                    </a>
                  )}
                </div>
                <div className="flex items-center space-x-2 ml-4">
                  {repo.private ? (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                      <Lock className="w-3 h-3 mr-1" />
                      Private
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                      <Globe className="w-3 h-3 mr-1" />
                      Public
                    </span>
                  )}
                </div>
              </div>

              {/* Description */}
              {repo.description && (
                <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                  {repo.description}
                </p>
              )}

              {/* Repository stats */}
              <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                {repo.language && (
                  <div className="flex items-center">
                    <Code className="w-3 h-3 text-purple-500 mr-1" />
                    {repo.language}
                  </div>
                )}
                <div className="flex items-center">
                  <Star className="w-3 h-3 text-yellow-500 mr-1" />
                  {repo.stargazers_count || 0}
                </div>
                <div className="flex items-center">
                  <GitFork className="w-3 h-3 text-blue-500 mr-1" />
                  {repo.forks || 0}
                </div>
                {(repo.open_issues || 0) > 0 && (
                  <div className="flex items-center">
                    <AlertCircle className="w-3 h-3 text-red-500 mr-1" />
                    {repo.open_issues} issues
                  </div>
                )}
                {formattedUpdatedAt && (
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    Updated {formattedUpdatedAt}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export { GithubRepositoryListDisplay };
