import React from 'react';
import { 
  <PERSON><PERSON>ext, 
  CheckCircle, 
  Link,
  ExternalLink,
  Copy
} from 'lucide-react';
import { GithubIcon } from 'components/icons/providers';
import { GithubWriteFileActionResult } from 'src/config/nangoModels';

type GithubWriteFileActionResultDisplayProps = {
  output: GithubWriteFileActionResult;
};

/**
 * Renders a rich display of a GitHub write file action result
 */
function GithubWriteFileActionResultDisplay({ output }: GithubWriteFileActionResultDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <GithubIcon className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No file write result available</p>
      </div>
    );
  }

  const getStatusColor = () => {
    switch (output.status?.toLowerCase()) {
      case 'success':
      case 'created':
      case 'updated':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'failed':
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <GithubIcon className="w-5 h-5 text-gray-900 dark:text-white mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            File Write Result
          </h3>
          <div className="ml-auto flex items-center space-x-2">
            {output.status && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor()}`}>
                <CheckCircle className="w-3 h-3 mr-1" />
                {output.status}
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* File operation status */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              File Operation Completed
            </h4>
          </div>
        </div>

        {/* File URL */}
        {output.url && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Location
            </div>
            <div className="flex items-center">
              <a
                href={output.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm flex items-center"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View file on GitHub
              </a>
            </div>
          </div>
        )}

        {/* Commit SHA */}
        {output.sha && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Commit SHA
            </div>
            <div className="flex items-center">
              <span className="font-mono text-sm bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-3 py-2 rounded border border-gray-300 dark:border-gray-600">
                {output.sha}
              </span>
              <button
                onClick={() => navigator.clipboard.writeText(output.sha)}
                className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-xs flex items-center"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </button>
            </div>
          </div>
        )}

        {/* Status details */}
        {output.status && (
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Operation Status
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
              <div className="capitalize">{output.status}</div>
            </div>
          </div>
        )}

        {/* Additional info */}
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center">
            <CheckCircle className="w-3 h-3 mr-1 text-green-600 dark:text-green-400" />
            The file has been successfully written to the repository.
          </div>
        </div>
      </div>
    </div>
  );
}

export { GithubWriteFileActionResultDisplay };
