import React from 'react';
import { Mail, FileText, Link } from 'lucide-react';
import { GmailDraftOutput } from 'src/config/nangoModels';

type GmailDraftOutputDisplayProps = {
  output: GmailDraftOutput;
};

/**
 * Renders a rich display of a Gmail draft creation result
 */
function GmailDraftOutputDisplay({ output }: GmailDraftOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No draft data available</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">Gmail Draft Created</h3>
        </div>
      </div>

      <div className="p-5 space-y-4">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="text-base font-medium text-gray-900 dark:text-white">
              Draft Email Created
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
              The draft has been saved to your Gmail account
            </p>
          </div>
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex items-start">
            <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
              Draft ID:
            </div>
            <div className="text-sm text-gray-800 dark:text-gray-200 break-all">{output.id}</div>
          </div>

          {output.threadId && (
            <div className="flex items-start">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400 min-w-32">
                Thread ID:
              </div>
              <div className="text-sm text-gray-800 dark:text-gray-200 break-all">
                {output.threadId}
              </div>
            </div>
          )}
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href="https://mail.google.com/mail/u/0/#drafts"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            <Link className="w-4 h-4 mr-1" />
            View in Gmail Drafts
          </a>
        </div>
      </div>
    </div>
  );
}

export { GmailDraftOutputDisplay };
