import React from 'react';
import { Mail, FileText, Link } from 'lucide-react';
import { GmailDraftOutput } from 'src/config/nangoModels';

type GmailDraftOutputDisplayProps = {
  output: GmailDraftOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Gmail draft creation result
 */
function GmailDraftOutputDisplay({ output, actionParameters }: GmailDraftOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No draft data available</p>
      </div>
    );
  }

  // Extract draft info from action parameters
  const recipient = actionParameters?.recipient;
  const subject = actionParameters?.subject;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            {subject ? `Gmail Draft: "${subject}"` : 'Gmail Draft Created'}
          </h3>
        </div>
        {recipient && (
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
            To: {recipient}
          </p>
        )}
      </div>

      <div className="p-5 space-y-4">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="text-base font-medium text-gray-900 dark:text-white">
              Draft Email Created
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-0.5">
              The draft has been saved to your Gmail account
            </p>
          </div>
        </div>

        {/* Draft details from action parameters */}
        {(subject || recipient || actionParameters?.body) && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
            <div className="space-y-2 text-sm">
              {recipient && (
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">To:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{recipient}</span>
                </div>
              )}
              {subject && (
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">Subject:</span>
                  <span className="ml-2 text-gray-800 dark:text-gray-200">{subject}</span>
                </div>
              )}
              {actionParameters?.body && (
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">Body:</span>
                  <div className="mt-1 text-gray-800 dark:text-gray-200 whitespace-pre-line line-clamp-3">
                    {actionParameters.body}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href="https://mail.google.com/mail/u/0/#drafts"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            <Link className="w-4 h-4 mr-1" />
            View in Gmail Drafts
          </a>
        </div> */}
      </div>
    </div>
  );
}

export { GmailDraftOutputDisplay };
