import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { cloneDeep, merge } from 'lodash-es';
import { invokeFunction } from 'utils/invokeFunction';

export interface TaskflowExecutionData {
  id: string;
  context: Record<
    string,
    {
      type: string;
      status: string;
      output?: any;
      hitl?: any;
      error?: any;
      steps?: Array<{
        type: string;
        provider?: string;
        completed?: boolean;
      }>;
      started?: boolean;
    }
  >;
  [key: string]: any;
}

export type ConfirmationState = 'idle' | 'executing' | 'success' | 'error' | 'cancelled';

function useTaskflowExecution(executionId: string): {
  execution: TaskflowExecutionData | null;
  loading: boolean;
  error: string | null;
  confirmationStates: Record<string, ConfirmationState>;
  handleSelectChange: (
    nodeId: string,
    fieldName: string,
    value: string,
    label: string
  ) => Promise<void>;
  handleConfirmation: (nodeId: string, confirmed: boolean) => Promise<void>;
} {
  const [execution, setExecution] = useState<TaskflowExecutionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [confirmationStates, setConfirmationStates] = useState<Record<string, ConfirmationState>>(
    {}
  );

  // Fetch execution data and subscribe to updates
  useEffect(() => {
    let subscription: any;

    const fetchData = async () => {
      try {
        setLoading(true);
        // Fetch the execution
        const { data, error } = await supabase
          .from('taskflow_executions')
          .select(
            `id,
            taskflowId,
            triggerData,
            context,
            result,
            startedAt,
            updatedAt,
            completedAt,
            status
            `
          )
          .eq('id', executionId)
          .single();

        if (error) {
          throw error;
        }

        setExecution(data);

        // Initialize confirmation states based on the execution context
        if (data && data.context) {
          const initialConfirmationStates: Record<string, ConfirmationState> = {};

          Object.entries(data.context).forEach(([nodeId, nodeData]: [string, any]) => {
            // Handle confirmation nodes
            if (nodeData.hitl && nodeData.hitl.type === 'confirmation') {
              if (nodeData.status === 'SUCCESS') {
                initialConfirmationStates[nodeId] = 'success';
              } else if (nodeData.status === 'ERROR') {
                initialConfirmationStates[nodeId] = 'error';
              } else if (nodeData.status === 'RUNNING') {
                initialConfirmationStates[nodeId] = 'executing';
              } else if (nodeData.status === 'PAUSED') {
                initialConfirmationStates[nodeId] = 'idle';
              }
            }
          });

          setConfirmationStates(initialConfirmationStates);
        }

        // Subscribe to updates
        // Create a new channel with a unique name
        const channelName = `taskflow-execution-${executionId}`;

        try {
          // First, try to get existing channels
          const channels = supabase.getChannels();

          // Remove any existing channel with the same name
          const existingChannel = channels.find(c => c.topic === channelName);
          if (existingChannel) {
            supabase.removeChannel(existingChannel);
          }

          subscription = supabase
            .channel(channelName)
            .on(
              'postgres_changes',
              {
                event: 'UPDATE',
                schema: 'public',
                table: 'taskflow_executions',
                filter: `id=eq.${executionId}`,
              },
              payload => {
                // Check if payload is valid
                if (!payload || !payload.new) {
                  return;
                }

                // FIXME: the backend state should never lead to this kind of inconsistency (where e.g. hitl forms are missing), but this does give some surity during re-runs etc.
                setExecution(old => cloneDeep(merge(old, payload.new)) as TaskflowExecutionData);
              }
            )
            .subscribe();
        } catch (subscriptionError) {
          // Subscription error handled silently
        }
      } catch (err) {
        setError('Failed to load taskflow execution');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Cleanup subscription and polling
    return () => {
      if (subscription) {
        try {
          subscription.unsubscribe();
        } catch (error) {
          // Unsubscribe error handled silently
        }
      }
    };
  }, [executionId]);

  // Handle select change - directly send to the server
  const handleSelectChange = async (
    nodeId: string,
    fieldName: string,
    value: string,
    label: string
  ) => {
    if (!value) return;

    try {
      // Call the resume-taskflow endpoint with force=true to replay subsequent nodes
      const { error } = await invokeFunction('resume-taskflow', {
        body: {
          executionId,
          nodeId,
          data: { [fieldName]: { value, label } },
          force: true, // Force replay of subsequent nodes
        },
      });

      if (error) {
        throw new Error(error.message || 'Failed to resume taskflow');
      }

      // The subscription will handle updating the UI
    } catch (err) {
      // Error handled silently
    }
  };

  // Handle confirmation - send the data to the server and track state
  const handleConfirmation = async (nodeId: string, confirmed: boolean) => {
    if (confirmed) {
      // Set state to executing
      setConfirmationStates(prev => {
        return { ...prev, [nodeId]: 'executing' as ConfirmationState };
      });
    } else {
      // Set state to cancelled
      setConfirmationStates(prev => {
        return { ...prev, [nodeId]: 'cancelled' as ConfirmationState };
      });
      return; // Don't make API call for cancellation
    }

    try {
      // Call the resume-taskflow endpoint
      const { error } = await invokeFunction('resume-taskflow', {
        body: {
          executionId,
          nodeId,
          data: { confirmed },
          force: true, // Force replay of subsequent nodes
        },
      });

      if (error) {
        throw new Error(error.message || 'Failed to resume taskflow');
      }

      // Set state to success
      setConfirmationStates(prev => {
        return { ...prev, [nodeId]: 'success' as ConfirmationState };
      });

      // The subscription will handle updating the execution data
    } catch (err) {
      // Set state to error
      setConfirmationStates(prev => {
        return { ...prev, [nodeId]: 'error' as ConfirmationState };
      });
    }
  };

  return {
    execution,
    loading,
    error,
    confirmationStates,
    handleSelectChange,
    handleConfirmation,
  };
}

export { useTaskflowExecution };
